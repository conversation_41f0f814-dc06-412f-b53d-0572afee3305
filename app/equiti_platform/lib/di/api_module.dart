import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_platform/di/interceptor_wrapper_values.dart';
import 'package:equiti_platform/network_connectivity/network_connectivity_manager.dart';
import 'package:equiti_platform/socket_request_response_tracker_interceptor.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:monitoring/monitoring.dart';
import 'package:network_logging/network_logging.dart';
import 'package:preferences/preferences.dart';
import 'package:socket_client/socket_client.dart';

@module
abstract class ApiModule {
  // Updated to use SocketEventLogger instead of SocketNetworkLoggerInterceptor
  @Named('SocketBaseUrl')
  @singleton
  String socketBaseUrl(AppConfig appConfig) => appConfig.socketBaseUrl;

  @Named('SocketBaseDemoUrl')
  @singleton
  String demoSocketBaseUrl(AppConfig appConfig) => appConfig.socketBaseDemoUrl;

  @Named('TradeBaseUrl')
  @singleton
  String tradeBaseUrl(AppConfig appConfig) => appConfig.baseUrl;

  @Named('TradeBaseDemoUrl')
  @singleton
  String tradeBaseDemoUrl(AppConfig appConfig) => appConfig.baseDemoUrl;

  @lazySingleton
  AuthService get authService => EmptyAuthService();

  @Named('mobileBffAuthInterceptor')
  @lazySingleton
  AuthInterceptor mobileBffAuthInterceptor(TokenManager tokenManager) {
    final interceptor = AuthInterceptor(
      dioInstanceName: 'mobileBffApiClient',
      onGetToken: () => tokenManager.getAccessToken(),
      onTokenRefresh: () async {
        await tokenManager.refreshAccessToken();
      },
      onNavigateToLogin: () {
        diContainer<EquitiTraderNavigation>().navigateToLogin();
      },
    );
    return interceptor;
  }

  @lazySingleton
  AuthInterceptor authInterceptor(TokenManager tokenManager) {
    final interceptor = AuthInterceptor(
      onGetToken: () => tokenManager.getAccessToken(),
      onTokenRefresh: () async {
        await tokenManager.refreshAccessToken();
      },
      onNavigateToLogin: () {
        diContainer<EquitiTraderNavigation>().navigateToLogin();
      },
    );
    return interceptor;
  }

  @lazySingleton
  TokenManager tokenManager(EquitiPreferences preferences) =>
      TokenManager(preferences, () => diContainer<AuthService>());

  @lazySingleton
  CurlInterceptor curlInterceptor() => const CurlInterceptor();

  @Named('mobileBffDioBuilder')
  @lazySingleton
  DioBuilder mobileBffDioBuilder(
    PrettyDioLogger dioLogger,
    AppConfig config,
    @Named('mobileBffAuthInterceptor') AuthInterceptor authInterceptor,
    CurlInterceptor curlInterceptor,
    NetworkLogManager networkLogManager,
    @Named('mobileBffNetworkConnectivityManager')
    NetworkConnectivityManager networkConnectivityManager,
  ) =>
      DioBuilder()
          .setBaseUrl(config.mobileBffBaseUrl)
          // .addInterceptor(dioLogger)
          .addInterceptor(networkLogManager.httpInterceptor())
          .addInterceptor(authInterceptor)
          .addInterceptor(networkConnectivityManager.interceptor)
          .addInterceptor(curlInterceptor)
          .addInterceptor(
            interceptorWrapper(
              InterceptorWrapperValues.instance.cachedEndpoints,
            ),
          )
          .withNativeAdapter()
          .withReporter();

  @Named('mobileBffApiClient')
  @lazySingleton
  ApiClientBase mobileBffApiClient(
    @Named('mobileBffDioBuilder') DioBuilder diobuilder,
  ) => DioApiClient(diobuilder.build());

  @lazySingleton
  DioBuilder dioBuilder(
    PrettyDioLogger dioLogger,
    @Named('TradeBaseUrl') String baseUrl,
    AuthInterceptor authInterceptor,
    CurlInterceptor curlInterceptor,
    NetworkLogManager networkLogManager,
    NetworkConnectivityManager networkConnectivityManager,
  ) =>
      DioBuilder()
          .setBaseUrl(baseUrl)
          // .addInterceptor(dioLogger)
          .addInterceptor(networkLogManager.httpInterceptor())
          .addInterceptor(authInterceptor)
          .addInterceptor(networkConnectivityManager.interceptor)
          .addInterceptor(curlInterceptor)
          .withNativeAdapter()
          .withReporter();

  @lazySingleton
  ApiClientBase apiClient(DioBuilder diobuilder) {
    final dio = diobuilder.build();
    return DioApiClient(dio);
  }

  @lazySingleton
  SocketClientBuilder socketClientBuilder(
    LoggerBase logger,
    @Named('SocketBaseUrl') String socketBaseUrl,
    TokenManager tokenManager,
    NetworkLogManager networkLogManager,
    AnalyticsService analyticsService,
  ) => SocketClientBuilder()
      .setLogger(logger)
      .setBaseUrl(socketBaseUrl)
      .setTokenProvider(() async => await tokenManager.getAccessToken() ?? "")
      .setRefreshTokenProvider(tokenManager.refreshAccessToken)
      .addInterceptor(SocketRequestResponseTrackerInterceptor(analyticsService))
      .addInterceptor(networkLogManager.socketInterceptor());

  @lazySingleton
  SocketClient socketClient(SocketClientBuilder socketClientBuilder) =>
      socketClientBuilder.build();

  @lazySingleton
  NetworkLogManager networkLogManager(LoggerBase logger, AppConfig appConfig) =>
      NetworkLogManagerFactory.create(appConfig.env.name);

  @lazySingleton
  UAEPassAuthRepository uaePassAuthRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClientBase,
    AppConfig appConfig,
  ) => UAEPassAuthRepository(
    apiClientBase,
    appConfig.uaePassEnvConfigs.redirectUri,
  );

  @Named('mobileBffNetworkConnectivityManager')
  @lazySingleton
  NetworkConnectivityManager networkConnectivityManager(
    GlobalKey<NavigatorState> navigatorKey,
  ) => NetworkConnectivityManager(
    navigatorKey: navigatorKey,
    dioInstanceName: 'mobileBffApiClient',
  );

  @lazySingleton
  NetworkConnectivityManager tradeNetworkConnectivityManager(
    GlobalKey<NavigatorState> navigatorKey,
  ) => NetworkConnectivityManager(navigatorKey: navigatorKey);
}
