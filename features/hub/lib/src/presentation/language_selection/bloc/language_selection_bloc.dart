import 'dart:async';
import 'dart:ui';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/domain/usecase/get_interface_preferences_use_case.dart';
import 'package:hub/src/domain/usecase/get_language_model_from_language_code_usecase.dart';
import 'package:hub/src/domain/usecase/save_interface_preferences_use_case.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:duplo/src/models/language_model.dart';

part 'language_selection_event.dart';
part 'language_selection_state.dart';
part 'language_selection_bloc.freezed.dart';

class LanguageSelectionBloc
    extends Bloc<LanguageSelectionEvent, LanguageSelectionState> {
  final GetInterfacePreferencesUseCase _getInterfacePreferencesUseCase;
  final SaveInterfacePreferencesUseCase _saveInterfacePreferencesUseCase;
  final GetLanguageModelFromLanguageCodeUsecase
  _getLanguageModelFromLanguageCodeUsecase;
  LanguageSelectionBloc({
    required GetLanguageModelFromLanguageCodeUsecase
    getLanguageModelFromLanguageCodeUsecase,
    required GetInterfacePreferencesUseCase getInterfacePreferencesUseCase,
    required SaveInterfacePreferencesUseCase saveInterfacePreferencesUseCase,
  }) : _saveInterfacePreferencesUseCase = saveInterfacePreferencesUseCase,
       _getInterfacePreferencesUseCase = getInterfacePreferencesUseCase,
       _getLanguageModelFromLanguageCodeUsecase =
           getLanguageModelFromLanguageCodeUsecase,
       super(LanguageSelectionInitial()) {
    on<_FetchLanguageSelection>(_onFetchLanguageSelection);
    on<_ChangeLanguageSelection>(_onChangeLanguageSelection);
  }

  FutureOr<void> _onFetchLanguageSelection(
    _FetchLanguageSelection event,
    Emitter<LanguageSelectionState> emit,
  ) async {
    emit(
      LanguageSelectionSuccess(
        language:
            _getLanguageModelFromLanguageCodeUsecase.getLanguageModelFromCode(
              await _getInterfacePreferencesUseCase.getLanguage(),
            ) ??
            LanguageModelOptions.defaultLanguage,
      ),
    );
  }

  FutureOr<void> _onChangeLanguageSelection(
    _ChangeLanguageSelection event,
    Emitter<LanguageSelectionState> emit,
  ) async {
    await diContainer<LocaleManager>().setLocale(Locale(event.language.code));
    _saveInterfacePreferencesUseCase.saveLanguage(event.language.code);

    if (!isClosed) {
      emit(LanguageSelectionSuccess(language: event.language));
    }
  }
}
