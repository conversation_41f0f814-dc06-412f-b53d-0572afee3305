name: hub
description: A central hub page for different products
version: 1.0.0+1
publish_to: none

environment:
  sdk: 3.8.1

dev_dependencies:
  dependency_validator: 5.0.2
  dart_code_metrics_presets: 2.22.0
  equiti_lint:
    path: ../../utilities/equiti_lint
  build_runner: 2.5.4
  flutter_gen_runner: 5.11.0
  injectable_generator: 2.7.0
  freezed: 3.0.6
  flutter_test:
    sdk: flutter
  mocktail: 1.0.4
  bdd_widget_test: 1.8.1
  bdd_steps:
    path: ../../utilities/bdd_steps
  equiti_test:
    path: ../../utilities/equiti_test
  toastification: 2.3.0
  socket_client:
    path: ../../utilities/socket_client
  json_serializable: 6.9.5
  vector_graphics_compiler: 1.1.17

dependencies:
  flutter:
    sdk: flutter
  monitoring:
    path: ../../utilities/monitoring
  duplo:
    path: ../../core/duplo
  domain:
    path: ../../core/domain
  flutter_animate: 4.5.2
  url_launcher: 6.3.2
  flutter_svg: 2.0.10+1
  flutter_custom_carousel: 0.1.0+1
  get_it: 8.0.3
  injectable: 2.5.0
  equiti_localization:
    path: ../../core/equiti_localization
  user_account:
    path: ../../core/user_account
  firebase:
    path: ../../core/firebase
  freezed_annotation: 3.0.0
  json_annotation: 4.9.0
  flutter_bloc: 9.1.1
  equiti_router:
    path: ../../utilities/equiti_router
  prelude:
    path: ../../utilities/prelude
  customer_support_chat:
    path: ../../utilities/customer_support_chat
  locale_manager: 0.0.1
  theme_manager: 0.0.1
  preferences:
    path: ../../utilities/preferences
  clock: 1.1.2
  api_client:
    path: ../../utilities/api_client
  vector_graphics: 1.1.19
  validator:
    path: ../../core/validator
  flutter_libphonenumber: 2.5.1
  visibility_detector: 0.4.0+2
  image_picker: 1.2.0
  feature_flags:
    path: ../../core/feature_flags
  package_info_plus: ^8.3.1
  login:
    path: ../../core/login
    
dependency_overrides:
  analyzer: ^6.3.0
  dart_style: ^3.0.1

flutter:
  uses-material-design: true
  generate: true
  fonts:
    - family: Helvetica
      fonts:
        - asset: assets/fonts/helvetica/Helvetica.ttf
          weight: 600
    - family: Gilroy
      fonts:
        - asset: assets/fonts/gilroy/Gilroy-Medium.ttf
          weight: 500
        - asset: assets/fonts/gilroy/Gilroy-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/gilroy/Gilroy-Bold.ttf
          weight: 700

  assets:
    - path: assets/images/
      transformers:
        - package: vector_graphics_compiler

flutter_gen:
  output: lib/src/assets/
  assets:
    exclude:
      - resources/mocks/**/*
      - assets/images/.DS_Store
      - assets/images/**/.DS_Store
    outputs:
      package_parameter_enabled: true
  integrations:
    flutter_svg: true
