import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/pool/symbol_quote_model_pool.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class SymbolQuoteRepository {
  SocketClient socketClient;
  final SymbolQuoteModelPool _modelPool = SymbolQuoteModelPool();
  final Map<String, int> _receiveCount = {};
  final Map<String, bool> _hasToggled = {};
  final Map<String, bool> _toggledState = {};

  SymbolQuoteRepository({required this.socketClient});

  TaskEither<Exception, Stream<SymbolQuoteModel>> subscribeToSymbolQuotes({
    required String symbol,
    required String accountNumber,
    required String subscriberId,
  }) {
    return socketClient
        .subscribe(
          path: 'productHub',
          eventType: TradingSocketEvent.quotes.subscribe,
          targets: ["QuotesUpdated"],
          subscriberId: subscriberId,
          args: [
            {
              "accountNumber": accountNumber,
              "symbols": [symbol],
            },
          ],
        )
        .map(
          (result) => result
              .map((data) {
                final arguments =
                    (data as Map<String, dynamic>)['arguments']
                        as Map<String, dynamic>;

                final nSymbol = arguments['symbol'] as String;

                // Track receive count
                _receiveCount[nSymbol] = (_receiveCount[nSymbol] ?? 0) + 1;
                final currentCount = _receiveCount[nSymbol]!;
                final shouldToggleMarket =
                    currentCount >= 10 && !(_hasToggled[nSymbol] ?? false);

                print(
                  "Symbol: $nSymbol, Count: $currentCount, Should toggle: $shouldToggleMarket",
                );

                // Get model from pool but DON'T use it directly
                final originalModel = _modelPool.getOrCreate(
                  nSymbol,
                  arguments,
                );

                // For testing: toggle isMarketOpen only once after 10 receives
                if (shouldToggleMarket) {
                  _hasToggled[nSymbol] = true;
                  // Store the target state (opposite of current)
                  _toggledState[nSymbol] = !originalModel.isMarketOpen;
                  print(
                    "Setting toggle state to ${_toggledState[nSymbol]} for symbol: $nSymbol",
                  );
                }

                // If we've toggled, create a NEW instance with the toggled state
                // This bypasses the pool's caching for our testing logic
                if (_hasToggled[nSymbol] ?? false) {
                  final toggledModel = originalModel.copyWith(
                    isMarketOpen: _toggledState[nSymbol]!,
                  );
                  print(
                    "Returning toggled state: ${toggledModel.isMarketOpen} for symbol: $nSymbol",
                  );
                  return toggledModel;
                }

                // Otherwise return the original model from pool
                print(
                  "Returning original state: ${originalModel.isMarketOpen} for symbol: $nSymbol",
                );
                return originalModel;
              })
              .where((data) => data.platformName == symbol),
        );
  }

  TaskEither<Exception, Stream<SymbolQuoteModel>> connectToSymbolsQuotes({
    required String subscriberId,
  }) => socketClient
      .subscribe(
        path: 'productHub',
        eventType: TradingSocketEvent.quotes.register,
        targets: ['QuotesUpdated'],
        subscriberId: subscriberId,
      )
      .map(
        (stream) => stream.map((data) {
          final arguments =
              (data as Map<String, dynamic>)['arguments']
                  as Map<String, dynamic>;

          final symbol = arguments['symbol'] as String;

          // Track receive count
          _receiveCount[symbol] = (_receiveCount[symbol] ?? 0) + 1;
          final currentCount = _receiveCount[symbol]!;
          final shouldToggleMarket =
              currentCount >= 10 && !(_hasToggled[symbol] ?? false);

          print(
            "Symbol: $symbol, Count: $currentCount, Should toggle: $shouldToggleMarket",
          );

          // Get model from pool but DON'T use it directly
          final originalModel = _modelPool.getOrCreate(symbol, arguments);

          // For testing: toggle isMarketOpen only once after 10 receives
          if (shouldToggleMarket) {
            _hasToggled[symbol] = true;
            // Store the target state (opposite of current)
            _toggledState[symbol] = !originalModel.isMarketOpen;
            print(
              "Setting toggle state to ${_toggledState[symbol]} for symbol: $symbol",
            );
          }

          // If we've toggled, create a NEW instance with the toggled state
          // This bypasses the pool's caching for our testing logic
          if (_hasToggled[symbol] ?? false) {
            final toggledModel = originalModel.copyWith(
              isMarketOpen: _toggledState[symbol]!,
            );
            print(
              "Returning toggled state: ${toggledModel.isMarketOpen} for symbol: $symbol",
            );
            return toggledModel;
          }

          // Otherwise return the original model from pool
          print(
            "Returning original state: ${originalModel.isMarketOpen} for symbol: $symbol",
          );
          return originalModel;
        }),
      );

  // Add methods to reset the testing state if needed
  void resetTestingStateForSymbol(String symbol) {
    _receiveCount.remove(symbol);
    _hasToggled.remove(symbol);
    _toggledState.remove(symbol);
  }

  void resetAllTestingState() {
    _receiveCount.clear();
    _hasToggled.clear();
    _toggledState.clear();
  }

  Future<void> updateQuotesBySymbols(
    List<String> symbols,
    EventType eventType,
    String accountNumber,
  ) {
    return socketClient.updateSubscription(
      path: 'productHub',
      eventType: eventType,
      targets: ['QuotesUpdated'],
      args: [
        {"accountNumber": accountNumber, "symbols": symbols},
      ],
    );
  }
}
