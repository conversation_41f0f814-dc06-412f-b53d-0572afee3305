// ignore_for_file: avoid-passing-async-when-sync-expected

import 'dart:async';

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/di/trading_environment_dependencies.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_selected_account_id_for_payments_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/wallet_details_bottom_sheet.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_settings_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_stats_card_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_toggle_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/add_new_account_button.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/server_not_supported_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/switch_account_app_bar.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/switch_account_portfolio_section.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_account_card_shimmer.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_account_card_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

class SwitchAccountScreen extends StatefulWidget {
  const SwitchAccountScreen({
    super.key,
    this.accountType = AccountType.trading,
  });

  final AccountType accountType;

  @override
  State<SwitchAccountScreen> createState() => _SwitchAccountScreenState();
}

class _SwitchAccountScreenState extends State<SwitchAccountScreen>
    with SingleTickerProviderStateMixin, PerformanceObserverMixin {
  late TabController _tabController;
  int _selectedCategoryIndex = 0;
  late TradingEnvironment _selectedTradingEnvironment = TradingEnvironment.live;
  late Future<void> _dependencyRegistrationFuture;

  @override
  void initState() {
    super.initState();
    _selectedCategoryIndex =
        widget.accountType == AccountType.landingWallet ? 1 : 0;

    // Register dependencies with correct environment
    _dependencyRegistrationFuture = TradingEnvironmentDependencies.register(
      isDemo: _selectedTradingEnvironment == TradingEnvironment.demo,
    );

    _tabController = TabController(length: 2, vsync: this);
    _tabController.animation!.addListener(_onTabAnimationChanged);
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      appBar: SwitchAccountAppBar(
        initialAccountType: _selectedTradingEnvironment,
        onAccountTypeChanged: (selectedAccountType) async {
          await TradingEnvironmentDependencies.register(
            isDemo: selectedAccountType == TradingEnvironment.demo,
          );
          if (mounted) {
            setState(() {
              _selectedTradingEnvironment = selectedAccountType;
              _selectedCategoryIndex = 0;
              _tabController.animateTo(_selectedCategoryIndex);
            });
          }
        },
      ),
      body: FutureBuilder<void>(
        future: _dependencyRegistrationFuture,
        builder: (builderContext, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error initializing: ${snapshot.error ?? 'Unknown error'}',
              ),
            );
          }

          return BlocProvider(
            key: ValueKey(_selectedTradingEnvironment),
            create:
                (_) => diContainer<AccountsBloc>()..add(AccountsEvent.fetch()),
            child: _SwitchAccountContent(
              selectedTradingEnvironment: _selectedTradingEnvironment,
              selectedCategoryIndex: _selectedCategoryIndex,
              onAccountCategoryChanged: (selectedCategory) {
                setState(() {
                  _selectedCategoryIndex = switch (selectedCategory) {
                    AccountCategory.accounts => 0,
                    AccountCategory.wallets => 1,
                  };
                });
                _tabController.animateTo(_selectedCategoryIndex);
              },
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _tabController.animation!.removeListener(_onTabAnimationChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabAnimationChanged() {
    final animationValue = _tabController.animation!.value;
    final newIndex = animationValue.round();

    if (newIndex != _selectedCategoryIndex) {
      setState(() {
        _selectedCategoryIndex = newIndex;
      });
    }
  }
}

class _WalletsWidget extends StatelessWidget {
  const _WalletsWidget({required this.footer});
  final Widget footer;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return BlocBuilder<AccountsBloc, AccountsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, accountState) {
        return switch (accountState.processState) {
          AccountsLoadingProcessState() => ListView(
            children: const [
              TradingAccountCardShimmer(
                accountCategory: AccountCategory.wallets,
              ),
              TradingAccountCardShimmer(
                accountCategory: AccountCategory.wallets,
              ),
              TradingAccountCardShimmer(
                accountCategory: AccountCategory.wallets,
              ),
            ],
          ),
          AccountsSuccessProcessState() => () {
            final walletsViewModel = accountState.wallets;
            final tradingAccounts = accountState.accounts;

            if (walletsViewModel.isEmpty) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  trader.Assets.images.noWallet.svg(),
                  const SizedBox(height: 16),
                  DuploText(
                    text: EquitiLocalization.of(context).trader_noWalletsFound,
                    style: textStyles.textLg,
                    fontWeight: DuploFontWeight.semiBold,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  DuploText(
                    text:
                        EquitiLocalization.of(
                          context,
                        ).trader_createWalletDescription,
                    style: textStyles.textSm,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              );
            }

            return CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsetsDirectional.only(start: 16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DuploText(
                          text:
                              EquitiLocalization.of(
                                context,
                              ).trader_cfdTradingAccounts,
                          style: textStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        Flexible(
                          child: DuploText(
                            text:
                                EquitiLocalization.of(
                                  context,
                                ).trader_switchAccountDescription,
                            style: textStyles.textXs,
                            color: theme.text.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 16)),
                SliverList.separated(
                  itemCount: walletsViewModel.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (listViewContext, index) {
                    final wallet = walletsViewModel[index];

                    return TradingAccountCardWidget(
                      isSelected: wallet.isSelected,
                      accountName: wallet.accountNumber ?? '',
                      equity: wallet.equity ?? 0,
                      profit: wallet.profit ?? 0,
                      balance: wallet.balance ?? 0,
                      currency: wallet.homeCurrency,
                      accountCategory: AccountCategory.wallets,
                      onTap: () {
                        final selectedAccount =
                            tradingAccounts.firstOrNullWhere(
                              (tradingAccount) =>
                                  tradingAccount.accountNumber ==
                                  wallet.accountNumber,
                            )!;

                        walletDetailsBottomSheet(
                          listViewContext,
                          selectedAccount,
                        ).then((_) {
                          listViewContext.read<AccountsBloc>().add(
                            const AccountsEvent.refreshAccounts(),
                          );
                        });
                      },
                    );
                  },
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: footer,
                    ),
                  ),
                ),
              ],
            );
          }(),
          AccountsErrorProcessState() => Center(
            child: DuploText(
              text: EquitiLocalization.of(context).trader_somethingWentWrong,
              style: textStyles.textSm,
            ),
          ),
        };
      },
    );
  }
}

class _TradingAccountsWidget extends StatelessWidget {
  const _TradingAccountsWidget(this._tradingEnvironment, this.footer);

  final TradingEnvironment _tradingEnvironment;
  final Widget footer;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;

    return BlocBuilder<AccountsBloc, AccountsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, accountState) {
        return switch (accountState.processState) {
          AccountsLoadingProcessState() => ListView(
            children: const [
              TradingAccountCardShimmer(),
              TradingAccountCardShimmer(),
              TradingAccountCardShimmer(),
            ],
          ),
          AccountsSuccessProcessState() => () {
            final tradingAccountsViewModel = accountState.tradingAccounts;
            final tradingAccounts = accountState.accounts;

            if (tradingAccountsViewModel.isEmpty) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  trader.Assets.images.noWallet.svg(),
                  const SizedBox(height: 16),
                  DuploText(
                    text:
                        EquitiLocalization.of(
                          context,
                        ).trader_noAccountsAvailable,
                    style: textStyles.textLg,
                    fontWeight: DuploFontWeight.semiBold,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  DuploText(
                    textAlign: TextAlign.center,
                    text:
                        EquitiLocalization.of(
                          context,
                        ).trader_noLiveAccountsDescription,
                    style: textStyles.textSm,
                  ),
                ],
              );
            }

            return CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsetsDirectional.only(start: 16.0),
                    child: _TradingAccountsHeaderWidget(_tradingEnvironment),
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 16)),
                SliverList.separated(
                  itemCount: tradingAccountsViewModel.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (listViewContext, index) {
                    final tradingAccountViewModel =
                        tradingAccountsViewModel[index];

                    return TradingAccountCardWidget(
                      isSelected: tradingAccountViewModel.isSelected,
                      accountName:
                          tradingAccountViewModel.nickName ??
                          tradingAccountViewModel.accountNumber ??
                          '',
                      tags: [
                        tradingAccountViewModel.platformType.displayName,
                        tradingAccountViewModel.platformTypeName,
                        if (tradingAccountViewModel.isSwapFree)
                          EquitiLocalization.of(context).trader_swapFree,
                      ],
                      equity: tradingAccountViewModel.equity ?? 0,
                      profit: tradingAccountViewModel.profit ?? 0,
                      marginLevel: tradingAccountViewModel.marginLevel ?? 0,
                      balance: tradingAccountViewModel.balance ?? 0,
                      currency: tradingAccountViewModel.homeCurrency,
                      accountCategory: AccountCategory.accounts,
                      onActionPressed: () {
                        final selectedAccount =
                            tradingAccounts.firstOrNullWhere(
                              (tradingAccount) =>
                                  tradingAccountViewModel.accountNumber ==
                                  tradingAccount.accountNumber,
                            )!;

                        showAccountSettingsBottomSheet<bool?>(
                          listViewContext,
                          selectedAccount,
                          _tradingEnvironment,
                          onDepositPressed: () {
                            listViewContext.read<AccountsBloc>().add(
                              AccountsEvent.goToDepositPaymentOptions(),
                            );
                            _saveSelectedAccountIdForPaymentsUseCase(
                              tradingAccountViewModel.accountId,
                            );
                          },
                          onWithdrawPressed: () {
                            listViewContext.read<AccountsBloc>().add(
                              AccountsEvent.goToWithdrawPaymentOptions(),
                            );
                            _saveSelectedAccountIdForPaymentsUseCase(
                              tradingAccountViewModel.accountId,
                            );
                          },
                          onTransferPressed: () {
                            listViewContext.read<AccountsBloc>().add(
                              AccountsEvent.goToTransferOptions(),
                            );
                            _saveSelectedAccountIdForPaymentsUseCase(
                              tradingAccountViewModel.accountId,
                            );
                          },
                        ).then((value) {
                          if (value == true) {
                            listViewContext.read<AccountsBloc>().add(
                              const AccountsEvent.refreshAccounts(),
                            );
                          }
                        });
                      },
                      onTap: () {
                        if (tradingAccountViewModel.platformType ==
                            PlatformType.mt4) {
                          serverNotSupportedBottomSheet(
                            listViewContext,
                            tradingAccounts.firstOrNullWhere(
                              (tradingAccount) =>
                                  tradingAccountViewModel.accountNumber ==
                                  tradingAccount.accountNumber,
                            )!,
                          );
                          return;
                        }
                        listViewContext.read<AccountsBloc>().add(
                          AccountsEvent.onAccountSelected(
                            accountId: tradingAccountViewModel.accountId,
                          ),
                        );
                      },
                    );
                  },
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: footer,
                    ),
                  ),
                ),
              ],
            );
          }(),
          AccountsErrorProcessState() => Center(
            child: DuploText(
              text: EquitiLocalization.of(context).trader_somethingWentWrong,
              style: textStyles.textSm,
            ),
          ),
        };
      },
    );
  }

  void _saveSelectedAccountIdForPaymentsUseCase(String accountId) async {
    await diContainer<SaveSelectedAccountIdForPaymentsUseCase>().call(
      accountId,
    );
  }
}

class _TradingAccountsHeaderWidget extends StatelessWidget {
  const _TradingAccountsHeaderWidget(this._tradingEnvironment);

  final TradingEnvironment _tradingEnvironment;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text:
              _tradingEnvironment == TradingEnvironment.live
                  ? EquitiLocalization.of(context).trader_cfdTradingAccounts
                  : EquitiLocalization.of(context).trader_demoTradingAccounts,
          style: textStyles.textMd,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
        ),
        Flexible(
          child: DuploText(
            text:
                EquitiLocalization.of(context).trader_switchAccountDescription,
            style: textStyles.textXs,
            color: theme.text.textTertiary,
          ),
        ),
      ],
    );
  }
}

class _SwitchAccountContent extends StatefulWidget {
  const _SwitchAccountContent({
    required this.selectedTradingEnvironment,
    required this.selectedCategoryIndex,
    required this.onAccountCategoryChanged,
  });
  final TradingEnvironment selectedTradingEnvironment;
  final int selectedCategoryIndex;
  final void Function(AccountCategory) onAccountCategoryChanged;

  @override
  State<_SwitchAccountContent> createState() => _SwitchAccountContentState();
}

class _SwitchAccountContentState extends State<_SwitchAccountContent>
    with PerformanceObserverMixin {
  late final SheetController _sheetController;

  initState() {
    super.initState();
    _sheetController = SheetController();
  }

  dispose() {
    _sheetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }
        try {
          await diContainer<UpdateTradingAccountBalanceHubUseCase>().call(
            eventType: TradingSocketEvent.accountBalance.unsubscribe,
            accountNumbers:
                context
                    .read<AccountsBloc>()
                    .state
                    .tradingAccounts
                    .map((account) => account.accountNumber!)
                    .toList(),
          );
          final selectedAccount =
              diContainer<GetSelectedAccountUseCase>().call();
          final isDemo = selectedAccount?.isDemo ?? false;
          TradingEnvironmentDependencies.register(isDemo: isDemo);
          Navigator.pop(context);
        } catch (error) {
          // Log error but don't fail the pop operation
          diContainer<LoggerBase>().logError(error);
        }
      },
      child: BlocBuilder<AccountsBloc, AccountsState>(
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          final textStyles = blocBuilderContext.duploTextStyles;

          return LayoutBuilder(
            builder: (layoutBuilderContext, constraints) {
              // Calculate heights for sheet positioning
              final portfolioSectionHeight = 138 + 40.0 + 36.0;
              ; // Portfolio section height + spacing
              final topOffset =
                  (widget.selectedTradingEnvironment ==
                              TradingEnvironment.live &&
                          ((state.processState is AccountsSuccessProcessState &&
                                  state.accounts.isNotEmpty) ||
                              state.processState
                                  is AccountsLoadingProcessState))
                      ? portfolioSectionHeight
                      : 24.0; // Just spacing if no portfolio section

              final maxChildSize = 1.0;
              double initialChildSize = ((constraints.maxHeight - topOffset) /
                      constraints.maxHeight)
                  .clamp(0.4, 1.0);

              // Expand to max size if difference is <= 0.1
              if ((maxChildSize - initialChildSize) <= 0.1) {
                initialChildSize = maxChildSize;
              }

              return Padding(
                padding: EdgeInsets.only(top: 4.0),
                child: Stack(
                  children: [
                    if (widget.selectedTradingEnvironment ==
                            TradingEnvironment.live &&
                        ((state.processState is AccountsSuccessProcessState &&
                                state.accounts.isNotEmpty) ||
                            state.processState is AccountsLoadingProcessState))
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(
                                vertical:
                                    state.availableCurrencies.length > 1
                                        ? 8
                                        : 16,
                                horizontal: 16,
                              ),
                              child:
                                  state.availableCurrencies.length > 1
                                      ? state.processState
                                              is AccountsLoadingProcessState
                                          ? DuploShimmerListItem(
                                            hasLeading: false,
                                            hasTrailing: false,
                                            height: 48,
                                          )
                                          : DuploInputDropDown(
                                            leading:
                                                FlagProvider.getFlagFromCurrencyCode(
                                                  state.selectedCurrency,
                                                ),
                                            label: DuploText(
                                              text: state.selectedCurrency,
                                              style:
                                                  DuploTextStyles.of(
                                                    context,
                                                  ).textMd,
                                              color: theme.text.textPrimary,
                                              fontWeight:
                                                  DuploFontWeight.medium,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            trailing: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child:
                                                  Assets.images.dropDownIc
                                                      .svg(),
                                            ),
                                            fullWidth: false,
                                            onTap: () {
                                              DuploDropDown.customBottomSheetSelector(
                                                context: context,
                                                bottomSheetTitle:
                                                    EquitiLocalization.of(
                                                      blocBuilderContext,
                                                    ).trader_selectCurrency,
                                                items:
                                                    state.availableCurrencies
                                                        .map(
                                                          (
                                                            currency,
                                                          ) => DropDownItemModel(
                                                            title: currency,
                                                            image:
                                                                FlagProvider.getFlagFromCurrencyCode(
                                                                  currency,
                                                                ),
                                                          ),
                                                        )
                                                        .toList(),
                                                selectedIndex: state
                                                    .availableCurrencies
                                                    .indexOf(
                                                      state.selectedCurrency,
                                                    ),
                                                onChanged: (index) {
                                                  final selectedCurrency = state
                                                      .availableCurrencies
                                                      .elementAtOrNull(index);
                                                  if (selectedCurrency !=
                                                      null) {
                                                    blocBuilderContext
                                                        .read<AccountsBloc>()
                                                        .add(
                                                          AccountsEvent.onCurrencySelected(
                                                            currency:
                                                                selectedCurrency,
                                                          ),
                                                        );
                                                  }
                                                },
                                              );
                                            },
                                          )
                                      : const SizedBox(),
                            ),
                            SwitchAccountPortfolioSection(
                              stats: [
                                AccountStatsCardWidget(
                                  label:
                                      EquitiLocalization.of(
                                        blocBuilderContext,
                                      ).trader_totalEquity,
                                  isLoading:
                                      state.processState
                                          is AccountsLoadingProcessState,
                                  value: state.totalEquity ?? 0,
                                  currency: state.selectedCurrency,
                                ),
                                AccountStatsCardWidget(
                                  label:
                                      EquitiLocalization.of(
                                        blocBuilderContext,
                                      ).trader_totalProfit,
                                  value: state.totalProfit ?? 0,
                                  isLoading:
                                      state.processState
                                          is AccountsLoadingProcessState,
                                  currency: state.selectedCurrency,
                                ),
                                AccountStatsCardWidget(
                                  label:
                                      EquitiLocalization.of(
                                        blocBuilderContext,
                                      ).trader_totalBalance,
                                  value: state.totalBalance ?? 0,
                                  isLoading:
                                      state.processState
                                          is AccountsLoadingProcessState,
                                  currency: state.selectedCurrency,
                                ),
                                AccountStatsCardWidget(
                                  label:
                                      EquitiLocalization.of(
                                        blocBuilderContext,
                                      ).trader_totalCredit,
                                  value: state.totalCredit ?? 0,
                                  isLoading:
                                      state.processState
                                          is AccountsLoadingProcessState,
                                  currency: state.selectedCurrency,
                                ),
                              ],
                              onStatPressed: (index) {
                                if (index == 0) {
                                  _showInfoModal(
                                    blocBuilderContext,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalEquity,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalEquityDescription,
                                  );
                                }
                                if (index == 1) {
                                  _showInfoModal(
                                    blocBuilderContext,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalProfit,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalProfitDescription,
                                  );
                                }
                                if (index == 2) {
                                  _showInfoModal(
                                    blocBuilderContext,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalBalance,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalBalanceDescription,
                                  );
                                }
                                if (index == 3) {
                                  _showInfoModal(
                                    blocBuilderContext,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalCredit,
                                    EquitiLocalization.of(
                                      blocBuilderContext,
                                    ).trader_totalCreditDescription,
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    Positioned.fill(
                      child: SheetViewport(
                        child: Sheet(
                          controller: _sheetController,
                          scrollConfiguration: const SheetScrollConfiguration(),
                          dragConfiguration: SheetDragConfiguration(),
                          decoration: MaterialSheetDecoration(
                            size: SheetSize.stretch,
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(24),
                            ),
                            color: theme.background.bgPrimary,
                          ),
                          initialOffset: SheetOffset(initialChildSize),
                          physics: BouncingSheetPhysics(),
                          snapGrid: SheetSnapGrid(
                            snaps: [
                              SheetOffset(initialChildSize),
                              SheetOffset(maxChildSize),
                            ],
                          ),
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.border.borderSecondary,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.08),
                                  blurRadius: 4,
                                  offset: Offset(0, -1),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                              child: SheetContentScaffold(
                                backgroundColor: theme.background.bgPrimary,
                                bottomBarVisibility: BottomBarVisibility.always(
                                  ignoreBottomInset: true,
                                ),
                                body: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: theme.border.borderSecondary,
                                    ),
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(24),
                                    ),
                                  ),
                                  padding:
                                      const EdgeInsetsDirectional.symmetric(
                                        vertical: 24,
                                        horizontal: 16,
                                      ),
                                  child:
                                      state.processState
                                                  is AccountsSuccessProcessState &&
                                              state.accounts.isEmpty
                                          ? Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            children: [
                                              Spacer(),
                                              trader.Assets.images.noAccount
                                                  .svg(),
                                              const SizedBox(height: 16),
                                              DuploText(
                                                text: EquitiLocalization.of(
                                                  context,
                                                ).trader_noAccountsMessage(
                                                  widget.selectedTradingEnvironment ==
                                                          TradingEnvironment
                                                              .demo
                                                      ? EquitiLocalization.of(
                                                        context,
                                                      ).trader_demo
                                                      : EquitiLocalization.of(
                                                        context,
                                                      ).trader_live,
                                                ),
                                                style: textStyles.textLg,
                                                fontWeight:
                                                    DuploFontWeight.semiBold,
                                                color: theme.text.textPrimary,
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 8),
                                              DuploText(
                                                text:
                                                    widget.selectedTradingEnvironment ==
                                                            TradingEnvironment
                                                                .demo
                                                        ? EquitiLocalization.of(
                                                          context,
                                                        ).trader_getStartedWithDemoAccount
                                                        : EquitiLocalization.of(
                                                          context,
                                                        ).trader_noLiveAccountsDescription,
                                                style: textStyles.textSm,
                                                textAlign: TextAlign.center,
                                                color: theme.text.textSecondary,
                                              ),
                                              Spacer(),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 16,
                                                    ),
                                                child: AddNewAccountButton(
                                                  selectedCategoryIndex:
                                                      widget
                                                          .selectedCategoryIndex,
                                                  onTap:
                                                      () => _navigateToCreateAccount(
                                                        blocBuilderContext,
                                                        thenCallback: () {
                                                          blocBuilderContext
                                                              .read<
                                                                AccountsBloc
                                                              >()
                                                              .add(
                                                                AccountsEvent.refreshAccounts(),
                                                              );
                                                        },
                                                      ),
                                                ),
                                              ),
                                            ],
                                          )
                                          : Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.stretch,
                                            children: [
                                              if (widget
                                                      .selectedTradingEnvironment ==
                                                  TradingEnvironment.live)
                                                AccountToggleWidget(
                                                  selectedValue: switch (widget
                                                      .selectedCategoryIndex) {
                                                    0 =>
                                                      AccountCategory.accounts,
                                                    1 =>
                                                      AccountCategory.wallets,
                                                    _ =>
                                                      throw UnimplementedError(),
                                                  },
                                                  onChanged:
                                                      widget
                                                          .onAccountCategoryChanged,
                                                  accountsCount:
                                                      state
                                                          .tradingAccounts
                                                          .length,
                                                  walletsCount:
                                                      state.wallets.length,
                                                ),
                                              const SizedBox(height: 16),

                                              Expanded(
                                                child: switch (widget
                                                    .selectedCategoryIndex) {
                                                  0 => _TradingAccountsWidget(
                                                    widget
                                                        .selectedTradingEnvironment,
                                                    AddNewAccountButton(
                                                      selectedCategoryIndex:
                                                          widget
                                                              .selectedCategoryIndex,
                                                      onTap:
                                                          () => _navigateToCreateAccount(
                                                            blocBuilderContext,
                                                            thenCallback: () {
                                                              blocBuilderContext
                                                                  .read<
                                                                    AccountsBloc
                                                                  >()
                                                                  .add(
                                                                    AccountsEvent.refreshAccounts(),
                                                                  );
                                                            },
                                                          ),
                                                    ),
                                                  ),
                                                  1 => _WalletsWidget(
                                                    footer: AddNewAccountButton(
                                                      selectedCategoryIndex:
                                                          widget
                                                              .selectedCategoryIndex,
                                                    ),
                                                  ),
                                                  _ =>
                                                    throw UnimplementedError(),
                                                },
                                              ),
                                            ],
                                          ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    final rawResult =
        diContainer<EquitiNavigatorBase>().globalData[EquitiTraderRouteSchema
            .switchAccountRoute
            .url];
    final result = rawResult is bool ? rawResult : false;
    if (result) {
      context.read<AccountsBloc>().add(AccountsEvent.refreshAccounts());
      diContainer<EquitiNavigatorBase>().globalData.remove(
        EquitiTraderRouteSchema.switchAccountRoute.url,
      );
    }

    // Subscribe to balance updates when route is popped (returning to this screen)
    context.read<AccountsBloc>().add(
      AccountsEvent.updateBalanceSubscription(subscribe: true),
    );
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    super.onRoutePushed(route);
    // Unsubscribe from balance updates when route is pushed (leaving this screen)
    if (route.settings.name != EquitiTraderRouteSchema.navBarRoute.url) {
      context.read<AccountsBloc>().add(
        AccountsEvent.updateBalanceSubscription(subscribe: false),
      );
    }
  }

  /// Determines the appropriate create account flow and navigates to the create account screen
  void _navigateToCreateAccount(
    BuildContext context, {
    required void Function() thenCallback,
  }) {
    // TODO:(AAKASH) check for max account length
    final createAccountFlow = _determineCreateAccountFlow(context);
    diContainer<EquitiTraderNavigation>().navigateToCreateAccountMain(
      createAccountFlow: createAccountFlow,
      thenCallback: thenCallback,
    );
  }

  /// Determines which create account flow to use based on environment and existing accounts
  CreateAccountFlow _determineCreateAccountFlow(BuildContext context) {
    if (widget.selectedTradingEnvironment == TradingEnvironment.demo) {
      return CreateAccountFlow.demoAccount;
    }

    final hasExistingAccounts =
        context.read<AccountsBloc>().state.accounts.isNotEmpty;

    return hasExistingAccounts
        ? CreateAccountFlow.additionalLiveAccount
        : CreateAccountFlow.firstLiveAccount;
  }

  /// Shows an informational modal sheet with a title and description
  void _showInfoModal(BuildContext context, String title, String description) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    DuploSheet.showModalSheetV2<void>(
      context,
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            DuploText(
              text: title,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: description,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
            ),
          ],
        ),
      ),
      bottomBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DuploButton.secondary(
          title: 'Dismiss',
          onTap: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }
}
