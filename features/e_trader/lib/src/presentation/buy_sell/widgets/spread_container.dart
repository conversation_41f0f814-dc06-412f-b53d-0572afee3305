import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class SpreadContainer extends StatelessWidget {
  const SpreadContainer({
    super.key,
    required this.spread,
    this.isDisabled = false,
  });
  final double spread;
  final bool isDisabled;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Center(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          borderRadius: BorderRadius.circular(4),
        ),
        child: DuploText(
          text: EquitiFormatter.decimalPatternDigits(
            value: spread,
            digits: 1,
            locale: Localizations.localeOf(context).toString(),
          ),
          style: duploTextStyles.textXs,
          fontWeight: DuploFontWeight.medium,
          color:
              isDisabled ? theme.foreground.fgDisabled : theme.text.textPrimary,
        ),
      ),
    );
  }
}
