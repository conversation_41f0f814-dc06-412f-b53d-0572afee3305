part of 'create_trade_bloc.dart';

@freezed
sealed class CreateTradeEvent with _$CreateTradeEvent {
  const factory CreateTradeEvent.handleSymbolStreamData(
    SymbolQuoteModel streams,
  ) = _HandleSymbolStreamData;
  const factory CreateTradeEvent.handleMarginStreamData(
    MarginRequirment streams,
  ) = _HandleMarginStreamData;
  const factory CreateTradeEvent.handleStreamError() = _HandleStreamError;

  const factory CreateTradeEvent.subscribe({
    required double orderSize,
    required EventType eventType,
  }) = _Subscribe;

  const factory CreateTradeEvent.updateMarginRequest({
    required double orderSize,
    required TradeType tradeType,
  }) = _UpdateMarginRequest;

  const factory CreateTradeEvent.updateTradeType(TradeType tradeType) =
      _UpdateTradeType;

  const factory CreateTradeEvent.resetTradeSelection() = _ResetTradeSelection;

  const factory CreateTradeEvent.tabIndexChanged(int tabIndex) =
      _TabIndexChanged;

  // //TODO: move these to their dedicated blocs
  // const factory CreateTradeEvent.placeMarketTrade() = _PlaceMarketTrade;
  // const factory CreateTradeEvent.placePendingOrderTrade() = _PlacePendingOrderTrade;
}
