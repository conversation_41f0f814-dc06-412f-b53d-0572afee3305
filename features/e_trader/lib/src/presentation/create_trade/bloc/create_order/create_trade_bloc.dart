import 'dart:async';

import 'package:domain/domain.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';

import 'package:e_trader/src/data/socket/margin_requirment_hub_request_model.dart';
import 'package:e_trader/src/domain/model/margin_requirment.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/calculate_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_margin_requirment_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_margin_requirment_use_case.dart';
import 'package:e_trader/src/presentation/create_trade/create_trade_widget.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:e_trader/src/presentation/model/pip_information_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'create_trade_bloc.freezed.dart';
part 'create_trade_event.dart';
part 'create_trade_state.dart';

class CreateTradeBloc extends Bloc<CreateTradeEvent, CreateTradeState>
    with DisposableMixin {
  final LoggerBase _logger;
  final SubscribeToMarginRequirmentUseCase _subscribeToMarginRequirmentUseCase;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final CalculateVolumeUseCase _calculateVolumeUseCase;
  final CreateTradeArgs _args;
  final UpdateMarginRequirmentUseCase _updateMarginRequirmentUseCase;

  CreateTradeBloc(
    this._getSelectedAccountUseCase,
    this._subscribeToMarginRequirmentUseCase,
    this._subscribeToSymbolQuotesUseCase,
    this._calculateVolumeUseCase,
    this._args,
    this._logger,
    this._updateMarginRequirmentUseCase,
  ) : super(CreateTradeState(tradeType: _args.initialTradeType)) {
    on<_Subscribe>((event, emit) async => await _initialize(event, emit));
    on<_HandleSymbolStreamData>(_handleSymbolStreams);
    on<_HandleMarginStreamData>(_handleMarginStreams);
    on<_HandleStreamError>((_, emit) => _handleStreamError(emit));
    on<_UpdateMarginRequest>(_updateMarginRequest);
    on<_UpdateTradeType>(_updateTradeType);
    on<_ResetTradeSelection>(_resetTradeSelection);
    on<_TabIndexChanged>(_tabIndexChanged);
  }

  FutureOr<void> _initialize(
    _Subscribe subscribeEvent,
    Emitter<CreateTradeState> emit,
  ) async {
    final selectedAccount = _getSelectedAccountUseCase();
    if (selectedAccount == null) {
      addError(Exception('No selected account'));
      return;
    }

    final result1 =
        await TaskEither<Exception, TradingAccountModel>.of(selectedAccount)
            .chainFirst((account) {
              emit(
                state.copyWith(
                  accountNumber: account.accountNumber,
                  accountCurrency: account.homeCurrency,
                ),
              );
              return TaskEither.of(account);
            })
            .flatMap(
              (account) => TaskEither.sequenceList([
                _getSymbolQuotesStream(),
                _getMarginRequirementHubStream(
                  orderSize: _args.minLot,
                  tradeType: state.tradeType ?? TradeType.buy,
                  eventType: subscribeEvent.eventType,
                ),
              ]),
            )
            .run();

    result1.fold(
      (error) {
        addError(error);
      },
      (streamList) {
        final symbolStream =
            streamList.elementAtOrNull(0) as Stream<SymbolQuoteModel>;
        final marginRequirementStream =
            streamList.elementAtOrNull(1) as Stream<MarginRequirment>;

        addSubscription(
          symbolStream.listen(
            (data) => add(CreateTradeEvent.handleSymbolStreamData(data)),
            onError: (Object e, StackTrace st) {
              addError(e, st);
              add(CreateTradeEvent.handleStreamError());
            },
          ),
        );
        addSubscription(
          marginRequirementStream.listen(
            (data) => add(CreateTradeEvent.handleMarginStreamData(data)),
            onError: (Object e, StackTrace st) {
              addError(e, st);
              add(CreateTradeEvent.handleStreamError());
            },
          ),
        );
      },
    );
  }

  TaskEither<Exception, Stream<SymbolQuoteModel>> _getSymbolQuotesStream() =>
      _subscribeToSymbolQuotesUseCase(
        symbol: _args.symbolCode,
        subscriberId: '${CreateTradeBloc}_$hashCode',
      );

  TaskEither<Exception, Stream<MarginRequirment>>
  _getMarginRequirementHubStream({
    required double orderSize,
    required TradeType tradeType,
    required EventType eventType,
  }) {
    final model = MarginRequirmentHubRequestModel(
      accountNumber: state.accountNumber!,
      symbol: _args.symbolCode,
      volume: _calculateVolumeUseCase(orderSize),
      tradeType: tradeType,
    );

    return _subscribeToMarginRequirmentUseCase(
      model,
      subscriberId: '${CreateTradeBloc}_$hashCode',
      eventType: eventType,
    );
  }

  MarginInformationModel _getMarginInformation(
    MarginRequirment marginRequirmentModel,
  ) {
    var marginInformation = state.marginInformation;
    if (marginInformation == null) {
      marginInformation = MarginInformationModel(
        marginFree: marginRequirmentModel.marginFree,
        requiredMargin: marginRequirmentModel.requiredMargin,
        requiredMarginPercentage:
            marginRequirmentModel.requiredMarginPercentage,
        maxLot: marginRequirmentModel.maxLot,
        minLot: marginRequirmentModel.minLot,
        notionalValue: marginRequirmentModel.notionalValue,
        pipInformation: PipInformationModel(
          pipValue: marginRequirmentModel.pipValue,
          onePip: marginRequirmentModel.onePip,
          pipMultipler: marginRequirmentModel.multiply,
        ),
      );
    } else {
      marginInformation.marginFree = marginRequirmentModel.marginFree;
      marginInformation.requiredMargin = marginRequirmentModel.requiredMargin;
      marginInformation.requiredMarginPercentage =
          marginRequirmentModel.requiredMarginPercentage;
      marginInformation.maxLot = marginRequirmentModel.maxLot;
      marginInformation.minLot = marginRequirmentModel.minLot;
      marginInformation.notionalValue = marginRequirmentModel.notionalValue;

      marginInformation.pipInformation.pipValue =
          marginRequirmentModel.pipValue;
      marginInformation.pipInformation.onePip = marginRequirmentModel.onePip;
      marginInformation.pipInformation.pipMultipler =
          marginRequirmentModel.multiply;
    }
    return marginInformation;
  }

  FutureOr<void> _handleStreamError(Emitter<CreateTradeState> emit) {
    emit(state.copyWith(processState: CreateOrderProcessState.disconnected()));
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    super.addError(error, stackTrace);
    _logger.logError(error, stackTrace: stackTrace);
  }

  FutureOr<void> _handleSymbolStreams(
    _HandleSymbolStreamData val,
    Emitter<CreateTradeState> emit,
  ) {
    final symbolQuoteModel = val.streams;

    state.symbolQuoteModel = symbolQuoteModel;

    emit(state.copyWith(processState: CreateOrderProcessState.connected()));
  }

  FutureOr<void> _updateMarginRequest(
    _UpdateMarginRequest val,
    Emitter<CreateTradeState> emit,
  ) {
    emit(state.copyWith(marginProcessState: MarginProcessState.loading()));
    final model = MarginRequirmentHubRequestModel(
      accountNumber: state.accountNumber!,
      symbol: _args.symbolCode,
      volume: _calculateVolumeUseCase(val.orderSize),
      tradeType: state.tradeType!,
    );

    _updateMarginRequirmentUseCase(
      model,
      TradingSocketEvent.marginRequirements.subscribe,
    );
  }

  FutureOr<void> _updateTradeType(
    _UpdateTradeType event,
    Emitter<CreateTradeState> emit,
  ) {
    emit(state.copyWith(tradeType: event.tradeType));
    add(
      CreateTradeEvent.updateMarginRequest(
        orderSize: _args.minLot,
        tradeType: event.tradeType,
      ),
    );
  }

  FutureOr<void> _resetTradeSelection(
    _ResetTradeSelection event,
    Emitter<CreateTradeState> emit,
  ) {
    emit(state.copyWith(tradeType: null, marginInformation: null));
  }

  FutureOr<void> _tabIndexChanged(
    _TabIndexChanged event,
    Emitter<CreateTradeState> emit,
  ) {
    emit(state.copyWith(currentTabIndex: event.tabIndex));
  }

  FutureOr<void> _handleMarginStreams(
    _HandleMarginStreamData event,
    Emitter<CreateTradeState> emit,
  ) {
    final marginInformation = _getMarginInformation(event.streams);

    state.marginInformation = marginInformation;

    emit(state.copyWith(marginProcessState: MarginProcessState.success()));
  }
}
