part of 'create_trade_bloc.dart';

@unfreezed
// ignore: prefer-immutable-bloc-state
sealed class CreateTradeState with _$CreateTradeState {
  const CreateTradeState._();
  factory CreateTradeState({
    String? accountNumber,
    @Default('') String accountCurrency,
    MarginInformationModel? marginInformation,
    @Default(MarginProcessState.initial())
    MarginProcessState marginProcessState,
    SymbolQuoteModel? symbolQuoteModel,
    TradeType? tradeType,
    @Default(CreateOrderProcessState.loading())
    CreateOrderProcessState processState,
    @Default(0) int currentTabIndex,
  }) = _CreateTradeState;

  BuySellButtonState? buyButtonState({bool forceDisabled = false}) {
    if (processState is CreateOrderLoadingProcessState) {
      return null;
    }
    if (processState is CreateOrderDisconnectedProcessState || forceDisabled) {
      return BuySellButtonState.disabled(symbolQuoteModel?.ask ?? 0.0);
    }
    if (processState is CreateOrderConnectedProcessState &&
        tradeType == TradeType.buy) {
      return BuySellButtonState.selected(symbolQuoteModel!.ask);
    }
    if (processState is PlacingOrderProcessState &&
        tradeType == TradeType.buy) {
      return BuySellButtonState.loading();
    }
    if (processState is OrderErrorProcessState && tradeType == TradeType.buy) {
      return BuySellButtonState.error();
    }
    return BuySellButtonState.active(symbolQuoteModel!.ask);
  }

  BuySellButtonState? sellButtonState({bool forceDisabled = false}) {
    if (processState is CreateOrderLoadingProcessState) {
      return null;
    }
    if (processState is CreateOrderDisconnectedProcessState || forceDisabled) {
      return BuySellButtonState.disabled(symbolQuoteModel?.bid ?? 0.0);
    }
    if (processState is CreateOrderConnectedProcessState &&
        tradeType == TradeType.sell) {
      return BuySellButtonState.selected(symbolQuoteModel!.bid);
    }
    if (processState is PlacingOrderProcessState &&
        tradeType == TradeType.sell) {
      return BuySellButtonState.loading();
    }
    if (processState is OrderErrorProcessState && tradeType == TradeType.sell) {
      return BuySellButtonState.error();
    }
    return BuySellButtonState.active(symbolQuoteModel!.bid);
  }
}

@freezed
sealed class CreateOrderProcessState with _$CreateOrderProcessState {
  const factory CreateOrderProcessState.loading() =
      CreateOrderLoadingProcessState;
  const factory CreateOrderProcessState.connected() =
      CreateOrderConnectedProcessState;
  const factory CreateOrderProcessState.disconnected() =
      CreateOrderDisconnectedProcessState;
  const factory CreateOrderProcessState.placingOrder() =
      PlacingOrderProcessState;
  const factory CreateOrderProcessState.orderSuccess() =
      OrderSuccessProcessState;
  const factory CreateOrderProcessState.orderError() = OrderErrorProcessState;
}

@freezed
sealed class MarginProcessState with _$MarginProcessState {
  const factory MarginProcessState.initial() = MarginInitial;
  const factory MarginProcessState.loading() = MarginLoading;
  const factory MarginProcessState.success() = MarginSuccess;
  const factory MarginProcessState.error(String message) = MarginError;
}
