// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_trade_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CreateTradeEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateTradeEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateTradeEvent()';
}


}

/// @nodoc
class $CreateTradeEventCopyWith<$Res>  {
$CreateTradeEventCopyWith(CreateTradeEvent _, $Res Function(CreateTradeEvent) __);
}


/// @nodoc


class _HandleSymbolStreamData implements CreateTradeEvent {
  const _HandleSymbolStreamData(this.streams);
  

 final  SymbolQuoteModel streams;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HandleSymbolStreamDataCopyWith<_HandleSymbolStreamData> get copyWith => __$HandleSymbolStreamDataCopyWithImpl<_HandleSymbolStreamData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HandleSymbolStreamData&&(identical(other.streams, streams) || other.streams == streams));
}


@override
int get hashCode => Object.hash(runtimeType,streams);

@override
String toString() {
  return 'CreateTradeEvent.handleSymbolStreamData(streams: $streams)';
}


}

/// @nodoc
abstract mixin class _$HandleSymbolStreamDataCopyWith<$Res> implements $CreateTradeEventCopyWith<$Res> {
  factory _$HandleSymbolStreamDataCopyWith(_HandleSymbolStreamData value, $Res Function(_HandleSymbolStreamData) _then) = __$HandleSymbolStreamDataCopyWithImpl;
@useResult
$Res call({
 SymbolQuoteModel streams
});


$SymbolQuoteModelCopyWith<$Res> get streams;

}
/// @nodoc
class __$HandleSymbolStreamDataCopyWithImpl<$Res>
    implements _$HandleSymbolStreamDataCopyWith<$Res> {
  __$HandleSymbolStreamDataCopyWithImpl(this._self, this._then);

  final _HandleSymbolStreamData _self;
  final $Res Function(_HandleSymbolStreamData) _then;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? streams = null,}) {
  return _then(_HandleSymbolStreamData(
null == streams ? _self.streams : streams // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,
  ));
}

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get streams {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.streams, (value) {
    return _then(_self.copyWith(streams: value));
  });
}
}

/// @nodoc


class _HandleMarginStreamData implements CreateTradeEvent {
  const _HandleMarginStreamData(this.streams);
  

 final  MarginRequirment streams;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HandleMarginStreamDataCopyWith<_HandleMarginStreamData> get copyWith => __$HandleMarginStreamDataCopyWithImpl<_HandleMarginStreamData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HandleMarginStreamData&&(identical(other.streams, streams) || other.streams == streams));
}


@override
int get hashCode => Object.hash(runtimeType,streams);

@override
String toString() {
  return 'CreateTradeEvent.handleMarginStreamData(streams: $streams)';
}


}

/// @nodoc
abstract mixin class _$HandleMarginStreamDataCopyWith<$Res> implements $CreateTradeEventCopyWith<$Res> {
  factory _$HandleMarginStreamDataCopyWith(_HandleMarginStreamData value, $Res Function(_HandleMarginStreamData) _then) = __$HandleMarginStreamDataCopyWithImpl;
@useResult
$Res call({
 MarginRequirment streams
});


$MarginRequirmentCopyWith<$Res> get streams;

}
/// @nodoc
class __$HandleMarginStreamDataCopyWithImpl<$Res>
    implements _$HandleMarginStreamDataCopyWith<$Res> {
  __$HandleMarginStreamDataCopyWithImpl(this._self, this._then);

  final _HandleMarginStreamData _self;
  final $Res Function(_HandleMarginStreamData) _then;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? streams = null,}) {
  return _then(_HandleMarginStreamData(
null == streams ? _self.streams : streams // ignore: cast_nullable_to_non_nullable
as MarginRequirment,
  ));
}

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginRequirmentCopyWith<$Res> get streams {
  
  return $MarginRequirmentCopyWith<$Res>(_self.streams, (value) {
    return _then(_self.copyWith(streams: value));
  });
}
}

/// @nodoc


class _HandleStreamError implements CreateTradeEvent {
  const _HandleStreamError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HandleStreamError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateTradeEvent.handleStreamError()';
}


}




/// @nodoc


class _Subscribe implements CreateTradeEvent {
  const _Subscribe({required this.orderSize, required this.eventType});
  

 final  double orderSize;
 final  EventType eventType;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscribeCopyWith<_Subscribe> get copyWith => __$SubscribeCopyWithImpl<_Subscribe>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Subscribe&&(identical(other.orderSize, orderSize) || other.orderSize == orderSize)&&(identical(other.eventType, eventType) || other.eventType == eventType));
}


@override
int get hashCode => Object.hash(runtimeType,orderSize,eventType);

@override
String toString() {
  return 'CreateTradeEvent.subscribe(orderSize: $orderSize, eventType: $eventType)';
}


}

/// @nodoc
abstract mixin class _$SubscribeCopyWith<$Res> implements $CreateTradeEventCopyWith<$Res> {
  factory _$SubscribeCopyWith(_Subscribe value, $Res Function(_Subscribe) _then) = __$SubscribeCopyWithImpl;
@useResult
$Res call({
 double orderSize, EventType eventType
});




}
/// @nodoc
class __$SubscribeCopyWithImpl<$Res>
    implements _$SubscribeCopyWith<$Res> {
  __$SubscribeCopyWithImpl(this._self, this._then);

  final _Subscribe _self;
  final $Res Function(_Subscribe) _then;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? orderSize = null,Object? eventType = null,}) {
  return _then(_Subscribe(
orderSize: null == orderSize ? _self.orderSize : orderSize // ignore: cast_nullable_to_non_nullable
as double,eventType: null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as EventType,
  ));
}


}

/// @nodoc


class _UpdateMarginRequest implements CreateTradeEvent {
  const _UpdateMarginRequest({required this.orderSize, required this.tradeType});
  

 final  double orderSize;
 final  TradeType tradeType;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateMarginRequestCopyWith<_UpdateMarginRequest> get copyWith => __$UpdateMarginRequestCopyWithImpl<_UpdateMarginRequest>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateMarginRequest&&(identical(other.orderSize, orderSize) || other.orderSize == orderSize)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType));
}


@override
int get hashCode => Object.hash(runtimeType,orderSize,tradeType);

@override
String toString() {
  return 'CreateTradeEvent.updateMarginRequest(orderSize: $orderSize, tradeType: $tradeType)';
}


}

/// @nodoc
abstract mixin class _$UpdateMarginRequestCopyWith<$Res> implements $CreateTradeEventCopyWith<$Res> {
  factory _$UpdateMarginRequestCopyWith(_UpdateMarginRequest value, $Res Function(_UpdateMarginRequest) _then) = __$UpdateMarginRequestCopyWithImpl;
@useResult
$Res call({
 double orderSize, TradeType tradeType
});




}
/// @nodoc
class __$UpdateMarginRequestCopyWithImpl<$Res>
    implements _$UpdateMarginRequestCopyWith<$Res> {
  __$UpdateMarginRequestCopyWithImpl(this._self, this._then);

  final _UpdateMarginRequest _self;
  final $Res Function(_UpdateMarginRequest) _then;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? orderSize = null,Object? tradeType = null,}) {
  return _then(_UpdateMarginRequest(
orderSize: null == orderSize ? _self.orderSize : orderSize // ignore: cast_nullable_to_non_nullable
as double,tradeType: null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,
  ));
}


}

/// @nodoc


class _UpdateTradeType implements CreateTradeEvent {
  const _UpdateTradeType(this.tradeType);
  

 final  TradeType tradeType;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateTradeTypeCopyWith<_UpdateTradeType> get copyWith => __$UpdateTradeTypeCopyWithImpl<_UpdateTradeType>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateTradeType&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType));
}


@override
int get hashCode => Object.hash(runtimeType,tradeType);

@override
String toString() {
  return 'CreateTradeEvent.updateTradeType(tradeType: $tradeType)';
}


}

/// @nodoc
abstract mixin class _$UpdateTradeTypeCopyWith<$Res> implements $CreateTradeEventCopyWith<$Res> {
  factory _$UpdateTradeTypeCopyWith(_UpdateTradeType value, $Res Function(_UpdateTradeType) _then) = __$UpdateTradeTypeCopyWithImpl;
@useResult
$Res call({
 TradeType tradeType
});




}
/// @nodoc
class __$UpdateTradeTypeCopyWithImpl<$Res>
    implements _$UpdateTradeTypeCopyWith<$Res> {
  __$UpdateTradeTypeCopyWithImpl(this._self, this._then);

  final _UpdateTradeType _self;
  final $Res Function(_UpdateTradeType) _then;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tradeType = null,}) {
  return _then(_UpdateTradeType(
null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,
  ));
}


}

/// @nodoc


class _ResetTradeSelection implements CreateTradeEvent {
  const _ResetTradeSelection();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResetTradeSelection);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateTradeEvent.resetTradeSelection()';
}


}




/// @nodoc


class _TabIndexChanged implements CreateTradeEvent {
  const _TabIndexChanged(this.tabIndex);
  

 final  int tabIndex;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TabIndexChangedCopyWith<_TabIndexChanged> get copyWith => __$TabIndexChangedCopyWithImpl<_TabIndexChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TabIndexChanged&&(identical(other.tabIndex, tabIndex) || other.tabIndex == tabIndex));
}


@override
int get hashCode => Object.hash(runtimeType,tabIndex);

@override
String toString() {
  return 'CreateTradeEvent.tabIndexChanged(tabIndex: $tabIndex)';
}


}

/// @nodoc
abstract mixin class _$TabIndexChangedCopyWith<$Res> implements $CreateTradeEventCopyWith<$Res> {
  factory _$TabIndexChangedCopyWith(_TabIndexChanged value, $Res Function(_TabIndexChanged) _then) = __$TabIndexChangedCopyWithImpl;
@useResult
$Res call({
 int tabIndex
});




}
/// @nodoc
class __$TabIndexChangedCopyWithImpl<$Res>
    implements _$TabIndexChangedCopyWith<$Res> {
  __$TabIndexChangedCopyWithImpl(this._self, this._then);

  final _TabIndexChanged _self;
  final $Res Function(_TabIndexChanged) _then;

/// Create a copy of CreateTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tabIndex = null,}) {
  return _then(_TabIndexChanged(
null == tabIndex ? _self.tabIndex : tabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc
mixin _$CreateTradeState {

 String? get accountNumber; set accountNumber(String? value); String get accountCurrency; set accountCurrency(String value); MarginInformationModel? get marginInformation; set marginInformation(MarginInformationModel? value); MarginProcessState get marginProcessState; set marginProcessState(MarginProcessState value); SymbolQuoteModel? get symbolQuoteModel; set symbolQuoteModel(SymbolQuoteModel? value); TradeType? get tradeType; set tradeType(TradeType? value); CreateOrderProcessState get processState; set processState(CreateOrderProcessState value); int get currentTabIndex; set currentTabIndex(int value);
/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateTradeStateCopyWith<CreateTradeState> get copyWith => _$CreateTradeStateCopyWithImpl<CreateTradeState>(this as CreateTradeState, _$identity);





@override
String toString() {
  return 'CreateTradeState(accountNumber: $accountNumber, accountCurrency: $accountCurrency, marginInformation: $marginInformation, marginProcessState: $marginProcessState, symbolQuoteModel: $symbolQuoteModel, tradeType: $tradeType, processState: $processState, currentTabIndex: $currentTabIndex)';
}


}

/// @nodoc
abstract mixin class $CreateTradeStateCopyWith<$Res>  {
  factory $CreateTradeStateCopyWith(CreateTradeState value, $Res Function(CreateTradeState) _then) = _$CreateTradeStateCopyWithImpl;
@useResult
$Res call({
 String? accountNumber, String accountCurrency, MarginInformationModel? marginInformation, MarginProcessState marginProcessState, SymbolQuoteModel? symbolQuoteModel, TradeType? tradeType, CreateOrderProcessState processState, int currentTabIndex
});


$MarginInformationModelCopyWith<$Res>? get marginInformation;$MarginProcessStateCopyWith<$Res> get marginProcessState;$SymbolQuoteModelCopyWith<$Res>? get symbolQuoteModel;$CreateOrderProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$CreateTradeStateCopyWithImpl<$Res>
    implements $CreateTradeStateCopyWith<$Res> {
  _$CreateTradeStateCopyWithImpl(this._self, this._then);

  final CreateTradeState _self;
  final $Res Function(CreateTradeState) _then;

/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountNumber = freezed,Object? accountCurrency = null,Object? marginInformation = freezed,Object? marginProcessState = null,Object? symbolQuoteModel = freezed,Object? tradeType = freezed,Object? processState = null,Object? currentTabIndex = null,}) {
  return _then(_self.copyWith(
accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,marginProcessState: null == marginProcessState ? _self.marginProcessState : marginProcessState // ignore: cast_nullable_to_non_nullable
as MarginProcessState,symbolQuoteModel: freezed == symbolQuoteModel ? _self.symbolQuoteModel : symbolQuoteModel // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel?,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as CreateOrderProcessState,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginProcessStateCopyWith<$Res> get marginProcessState {
  
  return $MarginProcessStateCopyWith<$Res>(_self.marginProcessState, (value) {
    return _then(_self.copyWith(marginProcessState: value));
  });
}/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res>? get symbolQuoteModel {
    if (_self.symbolQuoteModel == null) {
    return null;
  }

  return $SymbolQuoteModelCopyWith<$Res>(_self.symbolQuoteModel!, (value) {
    return _then(_self.copyWith(symbolQuoteModel: value));
  });
}/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CreateOrderProcessStateCopyWith<$Res> get processState {
  
  return $CreateOrderProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _CreateTradeState extends CreateTradeState {
   _CreateTradeState({this.accountNumber, this.accountCurrency = '', this.marginInformation, this.marginProcessState = const MarginProcessState.initial(), this.symbolQuoteModel, this.tradeType, this.processState = const CreateOrderProcessState.loading(), this.currentTabIndex = 0}): super._();
  

@override  String? accountNumber;
@override@JsonKey()  String accountCurrency;
@override  MarginInformationModel? marginInformation;
@override@JsonKey()  MarginProcessState marginProcessState;
@override  SymbolQuoteModel? symbolQuoteModel;
@override  TradeType? tradeType;
@override@JsonKey()  CreateOrderProcessState processState;
@override@JsonKey()  int currentTabIndex;

/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateTradeStateCopyWith<_CreateTradeState> get copyWith => __$CreateTradeStateCopyWithImpl<_CreateTradeState>(this, _$identity);





@override
String toString() {
  return 'CreateTradeState(accountNumber: $accountNumber, accountCurrency: $accountCurrency, marginInformation: $marginInformation, marginProcessState: $marginProcessState, symbolQuoteModel: $symbolQuoteModel, tradeType: $tradeType, processState: $processState, currentTabIndex: $currentTabIndex)';
}


}

/// @nodoc
abstract mixin class _$CreateTradeStateCopyWith<$Res> implements $CreateTradeStateCopyWith<$Res> {
  factory _$CreateTradeStateCopyWith(_CreateTradeState value, $Res Function(_CreateTradeState) _then) = __$CreateTradeStateCopyWithImpl;
@override @useResult
$Res call({
 String? accountNumber, String accountCurrency, MarginInformationModel? marginInformation, MarginProcessState marginProcessState, SymbolQuoteModel? symbolQuoteModel, TradeType? tradeType, CreateOrderProcessState processState, int currentTabIndex
});


@override $MarginInformationModelCopyWith<$Res>? get marginInformation;@override $MarginProcessStateCopyWith<$Res> get marginProcessState;@override $SymbolQuoteModelCopyWith<$Res>? get symbolQuoteModel;@override $CreateOrderProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$CreateTradeStateCopyWithImpl<$Res>
    implements _$CreateTradeStateCopyWith<$Res> {
  __$CreateTradeStateCopyWithImpl(this._self, this._then);

  final _CreateTradeState _self;
  final $Res Function(_CreateTradeState) _then;

/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountNumber = freezed,Object? accountCurrency = null,Object? marginInformation = freezed,Object? marginProcessState = null,Object? symbolQuoteModel = freezed,Object? tradeType = freezed,Object? processState = null,Object? currentTabIndex = null,}) {
  return _then(_CreateTradeState(
accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,marginProcessState: null == marginProcessState ? _self.marginProcessState : marginProcessState // ignore: cast_nullable_to_non_nullable
as MarginProcessState,symbolQuoteModel: freezed == symbolQuoteModel ? _self.symbolQuoteModel : symbolQuoteModel // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel?,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as CreateOrderProcessState,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginProcessStateCopyWith<$Res> get marginProcessState {
  
  return $MarginProcessStateCopyWith<$Res>(_self.marginProcessState, (value) {
    return _then(_self.copyWith(marginProcessState: value));
  });
}/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res>? get symbolQuoteModel {
    if (_self.symbolQuoteModel == null) {
    return null;
  }

  return $SymbolQuoteModelCopyWith<$Res>(_self.symbolQuoteModel!, (value) {
    return _then(_self.copyWith(symbolQuoteModel: value));
  });
}/// Create a copy of CreateTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CreateOrderProcessStateCopyWith<$Res> get processState {
  
  return $CreateOrderProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$CreateOrderProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState()';
}


}

/// @nodoc
class $CreateOrderProcessStateCopyWith<$Res>  {
$CreateOrderProcessStateCopyWith(CreateOrderProcessState _, $Res Function(CreateOrderProcessState) __);
}


/// @nodoc


class CreateOrderLoadingProcessState implements CreateOrderProcessState {
  const CreateOrderLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateOrderLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState.loading()';
}


}




/// @nodoc


class CreateOrderConnectedProcessState implements CreateOrderProcessState {
  const CreateOrderConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateOrderConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState.connected()';
}


}




/// @nodoc


class CreateOrderDisconnectedProcessState implements CreateOrderProcessState {
  const CreateOrderDisconnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateOrderDisconnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState.disconnected()';
}


}




/// @nodoc


class PlacingOrderProcessState implements CreateOrderProcessState {
  const PlacingOrderProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PlacingOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState.placingOrder()';
}


}




/// @nodoc


class OrderSuccessProcessState implements CreateOrderProcessState {
  const OrderSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState.orderSuccess()';
}


}




/// @nodoc


class OrderErrorProcessState implements CreateOrderProcessState {
  const OrderErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateOrderProcessState.orderError()';
}


}




/// @nodoc
mixin _$MarginProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarginProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarginProcessState()';
}


}

/// @nodoc
class $MarginProcessStateCopyWith<$Res>  {
$MarginProcessStateCopyWith(MarginProcessState _, $Res Function(MarginProcessState) __);
}


/// @nodoc


class MarginInitial implements MarginProcessState {
  const MarginInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarginInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarginProcessState.initial()';
}


}




/// @nodoc


class MarginLoading implements MarginProcessState {
  const MarginLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarginLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarginProcessState.loading()';
}


}




/// @nodoc


class MarginSuccess implements MarginProcessState {
  const MarginSuccess();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarginSuccess);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarginProcessState.success()';
}


}




/// @nodoc


class MarginError implements MarginProcessState {
  const MarginError(this.message);
  

 final  String message;

/// Create a copy of MarginProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarginErrorCopyWith<MarginError> get copyWith => _$MarginErrorCopyWithImpl<MarginError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarginError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'MarginProcessState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $MarginErrorCopyWith<$Res> implements $MarginProcessStateCopyWith<$Res> {
  factory $MarginErrorCopyWith(MarginError value, $Res Function(MarginError) _then) = _$MarginErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$MarginErrorCopyWithImpl<$Res>
    implements $MarginErrorCopyWith<$Res> {
  _$MarginErrorCopyWithImpl(this._self, this._then);

  final MarginError _self;
  final $Res Function(MarginError) _then;

/// Create a copy of MarginProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(MarginError(
null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
