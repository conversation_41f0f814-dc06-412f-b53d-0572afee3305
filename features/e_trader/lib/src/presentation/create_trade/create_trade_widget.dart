import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/flags/trader_flags.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/market_order_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/pending_order_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

typedef CreateTradeArgs =
    ({
      int digits,
      String symbolCode,
      String symbolImageUrl,
      String assetType,
      double minLot,
      double maxLot,
      bool isForex,
      double lotsSteps,
      TradeType? initialTradeType,
    });

class CreateTradeWidget extends StatefulWidget {
  const CreateTradeWidget({
    super.key,
    required this.args,
    this.onBannerPressed,
  });
  final CreateTradeArgs args;
  final VoidCallback? onBannerPressed;

  @override
  State<CreateTradeWidget> createState() => _CreateTradeWidgetState();
}

class _CreateTradeWidgetState extends State<CreateTradeWidget>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  int _currentTabIndex = 0;
  bool _didAutoDefaultToPending = false;
  late CreateTradeBloc _createTradeBloc;

  @override
  void initState() {
    super.initState();
    _createTradeBloc = context.read<CreateTradeBloc>();
    tabController = TabController(length: 2, vsync: this);
    _createTradeBloc.add(CreateTradeEvent.tabIndexChanged(_currentTabIndex));

    tabController.addListener(() {
      final newIndex = tabController.index;

      if (_currentTabIndex != newIndex) {
        _currentTabIndex = newIndex;
        _createTradeBloc.add(
          CreateTradeEvent.tabIndexChanged(_currentTabIndex),
        );
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext buildContext) {
    final localization = EquitiLocalization.of(buildContext);
    final traderFlags = diContainer<TraderFlags>();

    return BlocBuilder<CreateTradeBloc, CreateTradeState>(
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        if (!_didAutoDefaultToPending) {
          final shouldShowClosedMarketBanner =
              traderFlags.showClosedMarketBanner() &&
              (state.symbolQuoteModel?.isMarketOpen == false);
          if (shouldShowClosedMarketBanner && tabController.index != 1) {
            tabController.animateTo(1);
            _didAutoDefaultToPending = true;
            context.read<CreateTradeBloc>().add(
              CreateTradeEvent.tabIndexChanged(1),
            );
          }
        }

        return DuploTabBar(
          tabController: tabController,
          tabTitles: [
            DuploTabBarTitle(
              text: localization.trader_marketOrder,
              semanticsIdentifier: 'market_order_tab',
            ),
            DuploTabBarTitle(
              text: localization.trader_pendingOrder,
              semanticsIdentifier: 'pending_order_tab',
            ),
          ],
          tabViews: [
            _KeepAliveWrapper(
              child: BlocBuilder<CreateTradeBloc, CreateTradeState>(
                buildWhen: (previous, current) => previous != current,
                builder: (builderContext, innerState) {
                  final showBanner =
                      traderFlags.showClosedMarketBanner() &&
                      !(innerState.symbolQuoteModel?.isMarketOpen ?? false);

                  return switch (innerState.processState) {
                    CreateOrderLoadingProcessState() => const Center(
                      child: CircularProgressIndicator.adaptive(),
                    ),
                    CreateOrderDisconnectedProcessState() => Center(
                      child: Text(localization.trader_somethingWentWrong),
                    ),
                    _ => Column(
                      children: [
                        if (showBanner)
                          MarketClosedBanner(
                            onPressed: widget.onBannerPressed,
                            addBorders: true,
                          ),

                        Expanded(
                          child: MarketOrderWidget(
                            args: (
                              symbol: widget.args.symbolCode,
                              assetType: widget.args.assetType,
                              tradeType: innerState.tradeType,
                              accountNumber: innerState.accountNumber!,
                              marginInformation: innerState.marginInformation,
                              symbolQuoteModel: innerState.symbolQuoteModel!,
                              minLot: widget.args.minLot,
                              maxLot: widget.args.maxLot,
                              symbolImageUrl: widget.args.symbolImageUrl,
                              tabIndex: _currentTabIndex,
                              isForex: widget.args.isForex,
                              lotsSteps: widget.args.lotsSteps,
                            ),
                          ),
                        ),
                      ],
                    ),
                  };
                },
              ),
            ),
            _KeepAliveWrapper(
              child: BlocBuilder<CreateTradeBloc, CreateTradeState>(
                buildWhen: (previous, current) => previous != current,
                builder: (builderContext, innerState) {
                  return switch (innerState.processState) {
                    CreateOrderLoadingProcessState() => const Center(
                      child: CircularProgressIndicator.adaptive(),
                    ),
                    CreateOrderDisconnectedProcessState() => Center(
                      child: Text(localization.trader_somethingWentWrong),
                    ),
                    _ => Column(
                      children: [
                        Expanded(
                          child: PendingOrderWidget(
                            args: (
                              tradeType: innerState.tradeType,
                              assetType: widget.args.assetType,
                              accountNumber: innerState.accountNumber!,
                              marginInformation: innerState.marginInformation,
                              symbolQuoteModel: innerState.symbolQuoteModel!,
                              minLot: widget.args.minLot,
                              maxLot: widget.args.maxLot,
                              symbolImageUrl: widget.args.symbolImageUrl,
                              tabIndex: _currentTabIndex,
                              isForex: widget.args.isForex,
                              lotsSteps: widget.args.lotsSteps,
                            ),
                          ),
                        ),
                      ],
                    ),
                  };
                },
              ),
            ),
          ],
          isScrollable: false,
          isFlex: false,
        );
      },
    );
  }
}

class _KeepAliveWrapper extends StatefulWidget {
  const _KeepAliveWrapper({required this.child});
  final Widget child;

  @override
  State<_KeepAliveWrapper> createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<_KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}
