import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tap/duplo_tap.dart';
import 'package:duplo/src/components/text_chevron/text_chevron_widget.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_span.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';

class MarketClosedBanner extends StatelessWidget {
  const MarketClosedBanner({
    super.key,
    this.onPressed,
    this.addBorders = false,
  });
  final VoidCallback? onPressed;
  final bool addBorders;
  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);
    final duploTextStyles = context.duploTextStyles;
    return DuploTap(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
          color: theme.background.bgSecondary,
          border:
              addBorders
                  ? Border(
                    top: BorderSide(
                      color: theme.border.borderSecondary,
                      width: 1,
                    ),
                    bottom: BorderSide(
                      color: theme.border.borderSecondary,
                      width: 1,
                    ),
                  )
                  : null,
        ),
        child: TextChevronWidget(
          title: '',
          trailingText: l10n.trader_viewAll,
          onPressed: () => onPressed?.call(),
          leadingIcon: Assets.images.clockFastForward.svg(),
          titleWidget: DuploText.rich(
            spans: [
              DuploTextSpan(
                text: '${l10n.trader_market}: ',
                style: duploTextStyles.textSm,
                fontWeight: DuploFontWeight.medium,
                color: theme.text.textPrimary,
              ),
              DuploTextSpan(
                text: l10n.trader_closed,
                style: duploTextStyles.textSm,
                fontWeight: DuploFontWeight.semiBold,
                color: theme.text.textErrorPrimary,
              ),
            ],
          ),
          backgroundColor: context.duploTheme.background.bgSecondary,
        ),
      ),
    );
  }
}
